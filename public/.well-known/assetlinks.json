[{"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "com.app.daakia", "sha256_cert_fingerprints": ["7C:C9:E8:6B:B1:37:71:E6:AF:92:24:46:F2:3E:D1:E0:21:63:C4:A5:98:3D:C9:BD:7B:66:5B:63:89:A1:20:2D", "93:9C:F0:3E:33:FA:9A:D0:4C:B6:E3:EB:1F:1E:B7:B2:4F:14:2B:28:84:D8:72:03:1E:6B:B7:5A:C7:D2:89:E8", "70:F1:59:82:50:62:1B:33:C3:C9:A8:3D:5F:3E:94:98:4E:CE:0C:0C:74:6D:BF:0C:D0:CE:07:4A:42:72:F9:17"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "com.app.daakiameet", "sha256_cert_fingerprints": ["7D:4C:E3:F7:2D:D2:77:20:E3:2C:1C:E2:00:09:39:F1:F5:54:AA:F1:C3:75:48:8D:46:BE:E1:96:77:49:FD:AC", "4C:32:B1:60:14:25:8C:AA:B7:35:91:AC:66:CA:0A:85:DB:9D:CC:8A:23:65:DC:2D:D0:D1:59:2E:67:B0:B6:85"]}}]