# Deep Linking Setup Guide for Daakia Web App

## Overview
This guide explains how to set up deep linking for your Daakia web application, allowing users to open your web app directly from mobile devices instead of the browser.

## What is Deep Linking?
Deep linking allows mobile users to open your web app directly when they click on a link, creating a seamless native app-like experience. When properly configured:
- iOS users clicking a link to your domain will open your web app directly
- The app opens to the specific page/route they clicked on
- No browser interface is shown - it feels like a native app

## Current App Analysis
Your Daakia app has the following structure:
- **Base URL**: `/` (user routes)
- **Admin URL**: `/admin` (admin routes)
- **Key User Routes**:
  - `/` - Home page
  - `/login` - Login page
  - `/signup` - Signup page
  - `/meeting` - Jitsi meetings
  - `/v1/meeting` - Daakia VC meetings
  - `/recordings` - Meeting recordings
  - `/recording` - Transcript view
  - And many more...

## Files Already Created

### 1. Apple App Site Association File
**Location**: `public/.well-known/apple-app-site-association`

This file tells iOS devices which app to open when users visit your website. It's currently configured with placeholder values that need to be updated.

## Setup Steps

### Step 1: Get Your iOS App Information
Your friend needs to provide you with:

1. **Team ID**: Found in Apple Developer Account
   - Go to Apple Developer Console
   - Navigate to Membership section
   - Copy the Team ID (format: ABC123DEF4)

2. **Bundle ID**: Found in Xcode project
   - Open the iOS project in Xcode
   - Select the project in navigator
   - Go to General tab
   - Copy Bundle Identifier (format: com.company.appname)

### Step 2: Update the Apple App Site Association File
Replace `TEAM_ID.BUNDLE_ID` in the file with actual values:

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appIDs": [
          "S8QB4VV633.com.daakia.app"
        ],
        "paths": [
          "*"
        ],
        "components": [
          {
            "/": "/*"
          }
        ]
      }
    ]
  },
  "webcredentials": {
    "apps": [
      "S8QB4VV633.com.daakia.app"
    ]
  }
}
```

### Step 3: Deploy the File
The file must be accessible at:
`https://yourdomain.com/.well-known/apple-app-site-association`

**Important**: 
- No file extension (.json)
- Must be served with HTTPS
- Must return `Content-Type: application/json`

### Step 4: Configure Your Web Server
Ensure your web server serves the file correctly:

**For Apache (.htaccess)**:
```apache
<Files "apple-app-site-association">
    Header set Content-Type application/json
</Files>
```

**For Nginx**:
```nginx
location /.well-known/apple-app-site-association {
    add_header Content-Type application/json;
}
```

### Step 5: iOS App Configuration (Your Friend's Part)
Your friend needs to configure the iOS app to handle deep links:

1. **Add Associated Domains Capability**:
   - In Xcode, select the project
   - Go to Signing & Capabilities
   - Add "Associated Domains" capability
   - Add domain: `applinks:yourdomain.com`

2. **Handle Deep Links in App Code**:
   ```swift
   // In AppDelegate or SceneDelegate
   func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
       guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
             let url = userActivity.webpageURL else {
           return false
       }
       
       // Handle the deep link URL
       handleDeepLink(url: url)
       return true
   }
   ```

## Testing Deep Links

### Method 1: Physical Device
1. Install the iOS app on a physical device
2. Send yourself a text message with your website URL
3. Tap the link - it should open your app instead of Safari

### Method 2: iOS Simulator
```bash
xcrun simctl openurl booted https://yourdomain.com/meeting
```

### Method 3: Apple's AASA Validator
Use Apple's validator to check your file:
`https://search.developer.apple.com/appsearch-validation-tool/`

## Common Issues and Solutions

### Issue 1: Links Open in Browser Instead of App
**Causes**:
- AASA file not accessible
- Wrong Team ID or Bundle ID
- Apple's CDN hasn't cached your file yet (can take 24 hours)

**Solutions**:
- Verify file is accessible at the correct URL
- Check Team ID and Bundle ID are correct
- Wait for Apple's CDN to update

### Issue 2: AASA File Not Found
**Causes**:
- File not in correct location
- Web server not configured properly
- HTTPS not enabled

**Solutions**:
- Ensure file is at `/.well-known/apple-app-site-association`
- Configure web server to serve the file
- Enable HTTPS on your domain

### Issue 3: App Doesn't Handle Deep Links
**Causes**:
- Associated Domains not configured in iOS app
- Deep link handling code not implemented

**Solutions**:
- Add Associated Domains capability in Xcode
- Implement deep link handling in app code

## Advanced Configuration

### Specific Path Handling
If you want to handle only specific routes:

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appIDs": ["TEAM_ID.BUNDLE_ID"],
        "paths": [
          "/meeting/*",
          "/v1/meeting/*",
          "/recordings",
          "/login",
          "/signup"
        ]
      }
    ]
  }
}
```

### Multiple Apps
If you have multiple iOS apps:

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appIDs": [
          "TEAM_ID.com.daakia.app",
          "TEAM_ID.com.daakia.admin"
        ],
        "paths": ["*"]
      }
    ]
  }
}
```

## Next Steps
1. Get Team ID and Bundle ID from your friend
2. Update the AASA file with correct values
3. Deploy your web app with the AASA file
4. Have your friend configure the iOS app
5. Test the deep linking functionality

## Resources
- [Apple's Universal Links Documentation](https://developer.apple.com/ios/universal-links/)
- [AASA File Validator](https://search.developer.apple.com/appsearch-validation-tool/)
- [Branch.io Deep Linking Guide](https://help.branch.io/developers-hub/docs/ios-universal-links)
