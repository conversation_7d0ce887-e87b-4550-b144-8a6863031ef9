#!/usr/bin/env node

/**
 * Script to verify Apple App Site Association file for deep linking
 * Usage: node scripts/verify-deep-linking.js [domain]
 * Example: node scripts/verify-deep-linking.js https://yourdomain.com
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Get command line arguments
const args = process.argv.slice(2);
const domain = args[0];

console.log('🔍 Deep Linking Verification Tool\n');

// Check local file first
function checkLocalFile() {
  console.log('📁 Checking local AASA file...');
  
  const localPath = path.join(__dirname, '..', 'public', '.well-known', 'apple-app-site-association');
  
  if (!fs.existsSync(localPath)) {
    console.log('❌ Local AASA file not found at:', localPath);
    return false;
  }
  
  try {
    const content = fs.readFileSync(localPath, 'utf8');
    const parsed = JSON.parse(content);
    
    console.log('✅ Local AASA file found and valid JSON');
    
    // Check for placeholder values
    const appIds = parsed.applinks?.details?.[0]?.appIDs || [];
    if (appIds.some(id => id.includes('TEAM_ID') || id.includes('BUNDLE_ID'))) {
      console.log('⚠️  Warning: AASA file contains placeholder values');
      console.log('   Please update with actual Team ID and Bundle ID');
      console.log('   Use: node scripts/update-deep-linking.js <TEAM_ID> <BUNDLE_ID>');
    } else {
      console.log('✅ AASA file appears to be configured with real values');
      console.log('   App IDs:', appIds);
    }
    
    return true;
  } catch (error) {
    console.log('❌ Local AASA file is invalid JSON:', error.message);
    return false;
  }
}

// Check remote file
function checkRemoteFile(domain) {
  return new Promise((resolve) => {
    console.log(`\n🌐 Checking remote AASA file at: ${domain}/.well-known/apple-app-site-association`);
    
    const parsedUrl = url.parse(domain);
    const isHttps = parsedUrl.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: '/.well-known/apple-app-site-association',
      method: 'GET',
      headers: {
        'User-Agent': 'Deep-Link-Verifier/1.0'
      }
    };
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Response Status: ${res.statusCode}`);
        console.log(`📋 Content-Type: ${res.headers['content-type'] || 'Not specified'}`);
        
        if (res.statusCode === 200) {
          try {
            const parsed = JSON.parse(data);
            console.log('✅ Remote AASA file is accessible and valid JSON');
            
            const appIds = parsed.applinks?.details?.[0]?.appIDs || [];
            console.log('📱 App IDs found:', appIds);
            
            // Check content type
            const contentType = res.headers['content-type'];
            if (contentType && contentType.includes('application/json')) {
              console.log('✅ Correct Content-Type header');
            } else {
              console.log('⚠️  Warning: Content-Type should be application/json');
            }
            
          } catch (error) {
            console.log('❌ Remote AASA file is not valid JSON:', error.message);
          }
        } else {
          console.log('❌ Remote AASA file is not accessible');
          console.log('   Make sure the file is deployed and your server is configured correctly');
        }
        
        resolve();
      });
    });
    
    req.on('error', (error) => {
      console.log('❌ Error accessing remote AASA file:', error.message);
      resolve();
    });
    
    req.setTimeout(10000, () => {
      console.log('❌ Timeout accessing remote AASA file');
      req.destroy();
      resolve();
    });
    
    req.end();
  });
}

// Main verification function
async function verify() {
  const localValid = checkLocalFile();
  
  if (domain) {
    await checkRemoteFile(domain);
  } else {
    console.log('\n💡 To check remote file, provide domain:');
    console.log('   node scripts/verify-deep-linking.js https://yourdomain.com');
  }
  
  console.log('\n📚 Additional Resources:');
  console.log('   • Apple AASA Validator: https://search.developer.apple.com/appsearch-validation-tool/');
  console.log('   • Universal Links Guide: https://developer.apple.com/ios/universal-links/');
  
  if (localValid && domain) {
    console.log('\n🎉 Verification complete! If both local and remote checks passed,');
    console.log('   your deep linking should work once the iOS app is configured.');
  }
}

// Run verification
verify().catch(console.error);
