#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update Apple App Site Association file for deep linking
 * Usage: node scripts/update-deep-linking.js <TEAM_ID> <BUNDLE_ID>
 * Example: node scripts/update-deep-linking.js S8QB4VV633 com.daakia.app
 */

const fs = require('fs');
const path = require('path');

// Get command line arguments
const args = process.argv.slice(2);

if (args.length !== 2) {
  console.error('Usage: node scripts/update-deep-linking.js <TEAM_ID> <BUNDLE_ID>');
  console.error('Example: node scripts/update-deep-linking.js S8QB4VV633 com.daakia.app');
  process.exit(1);
}

const [teamId, bundleId] = args;
const appId = `${teamId}.${bundleId}`;

// Validate inputs
if (!teamId.match(/^[A-Z0-9]{10}$/)) {
  console.error('Error: Team ID should be 10 characters (letters and numbers)');
  console.error('Example: S8QB4VV633');
  process.exit(1);
}

if (!bundleId.match(/^[a-zA-Z0-9.-]+$/)) {
  console.error('Error: Bundle ID should be in reverse domain format');
  console.error('Example: com.daakia.app');
  process.exit(1);
}

// Create the AASA content
const aasaContent = {
  "applinks": {
    "apps": [],
    "details": [
      {
        "appIDs": [appId],
        "paths": ["*"],
        "components": [
          {
            "/": "/*"
          }
        ]
      }
    ]
  },
  "webcredentials": {
    "apps": [appId]
  }
};

// File path
const aasaPath = path.join(__dirname, '..', 'public', '.well-known', 'apple-app-site-association');

try {
  // Ensure directory exists
  const dir = path.dirname(aasaPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  // Write the file
  fs.writeFileSync(aasaPath, JSON.stringify(aasaContent, null, 2));
  
  console.log('✅ Apple App Site Association file updated successfully!');
  console.log(`📱 App ID: ${appId}`);
  console.log(`📁 File location: ${aasaPath}`);
  console.log('');
  console.log('Next steps:');
  console.log('1. Deploy your web app');
  console.log('2. Verify the file is accessible at: https://yourdomain.com/.well-known/apple-app-site-association');
  console.log('3. Configure your iOS app with Associated Domains capability');
  console.log('4. Test the deep linking functionality');
  
} catch (error) {
  console.error('❌ Error updating AASA file:', error.message);
  process.exit(1);
}
